# Repository Guidelines

## Project Structure & Module Organization
Core training logic lives in `ddpm_conditional.py`, which wires together the diffusion schedule, EMA wrapper, and the training loop. Network definitions (UNet, attention blocks, EMA helper) are in `modules.py`, while `utils.py` contains data loading, logging setup, and image IO utilities. Generated assets are kept under `models/DDPM_conditional`, `results/DDPM_conditional`, and `runs/DDPM_conditional`; keep large checkpoints and TensorBoard logs inside those folders to stay consistent. Update the dataset root in `ddpm_conditional.py:167` before training so that `torchvision.datasets.ImageFolder` can resolve your local CIFAR-10 directory.

## Build, Test, and Development Commands
Use `python ddpm_conditional.py` to start a conditional DDPM training run with the default hyperparameters and EMA tracking. Launch TensorBoard with `tensorboard --logdir runs` to inspect losses and sampling metrics logged during training. After you have weights, generate conditional samples in an interactive session by instantiating `UNet_conditional` and calling `Diffusion.sample(...)` as shown in the README.

## Coding Style & Naming Conventions
Follow the existing Python style: 4-space indentation, `snake_case` for functions and variables, and `CamelCase` for classes. Keep modules importable—avoid placing executable code at import time outside `if __name__ == "__main__"`. When adding utilities, colocate reusable components in `utils.py` and document any new arguments directly in the function docstring.

## Testing Guidelines
There is no automated test suite, so rely on deterministic sanity checks. Before opening a PR, run `python ddpm_conditional.py` for at least one epoch with a tiny subset to confirm the loss decreases and checkpoints write to `models/`. Validate inference paths by sampling a small batch and confirming saved grids appear in `results/`. Record GPU/CPU device expectations in your change description if the new feature alters runtime requirements.

## Commit & Pull Request Guidelines
Stick to short, imperative commit messages (e.g., `Add cosine beta schedule`) similar in spirit to the existing history. Reference related issues when applicable and explain the motivation, user impact, and test evidence in the PR body. Include screenshots or sample grids for changes that affect outputs, and note any migration steps (data path updates, checkpoint conversions) reviewers must perform locally.

## Experiment & Security Notes
Keep credentials and private dataset paths out of the codebase—use environment variables or local config files ignored by git. Large experiment artifacts belong in `results/` or external storage; trim or compress before committing. When sharing pretrained weights, prefer publishing download instructions instead of checking binaries into the repo.
